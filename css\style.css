/* ===== AI TECHNOLOGY THEME VARIABLES ===== */
:root {
    --primary-color: #dc2626;
    --secondary-color: #7c2d12;
    --accent-color: #991b1b;
    --neon-red: #ef4444;
    --neon-maroon: #7f1d1d;
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a0a0a;
    --bg-tertiary: #2d1b1b;
    --text-primary: #ffffff;
    --text-secondary: #fca5a5;
    --text-muted: #dc2626;
    --border-color: rgba(220, 38, 38, 0.3);
    --shadow-red: rgba(220, 38, 38, 0.4);
    --shadow-maroon: rgba(127, 29, 29, 0.6);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-secondary: linear-gradient(135deg, var(--neon-red), var(--neon-maroon));
    --gradient-ai: linear-gradient(45deg, #dc2626, #7c2d12, #991b1b, #ef4444);
    --glow-red: 0 0 20px rgba(220, 38, 38, 0.5);
    --glow-intense: 0 0 40px rgba(220, 38, 38, 0.8);
}

/* ===== RESET AND BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 30%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(127, 29, 29, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 50% 10%, rgba(239, 68, 68, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 10% 80%, rgba(153, 27, 27, 0.12) 0%, transparent 50%);
    z-index: -1;
    animation: aiBackground 20s ease-in-out infinite;
}

@keyframes aiBackground {
    0%, 100% { opacity: 0.8; transform: scale(1) rotate(0deg); }
    25% { opacity: 1; transform: scale(1.05) rotate(1deg); }
    50% { opacity: 0.9; transform: scale(0.95) rotate(-1deg); }
    75% { opacity: 1; transform: scale(1.02) rotate(0.5deg); }
}

/* ===== AI PARTICLES BACKGROUND ===== */
.ai-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: float 15s infinite linear;
    box-shadow: var(--glow-red);
}

@keyframes float {
    0% { transform: translateY(100vh) translateX(0px) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100px) translateX(100px) rotate(360deg); opacity: 0; }
}

/* ===== AI NAVIGATION ===== */
.nav-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid var(--border-color);
    z-index: 1000;
    box-shadow: 0 4px 20px var(--shadow-red);
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.brand-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.3rem;
    text-shadow: var(--glow-red);
}

.brand-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
    animation: brandPulse 3s ease-in-out infinite;
    text-shadow: var(--glow-red);
}

@keyframes brandPulse {
    0%, 100% { transform: scale(1); text-shadow: 0 0 10px var(--primary-color); }
    50% { transform: scale(1.1); text-shadow: 0 0 20px var(--primary-color); }
}

.brand-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from { filter: drop-shadow(0 0 5px var(--primary-color)); }
    to { filter: drop-shadow(0 0 15px var(--primary-color)); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    border-color: var(--primary-color);
    box-shadow: var(--glow-red);
    text-shadow: 0 0 10px var(--primary-color);
}

.nav-link:hover::before,
.nav-link.active::before {
    opacity: 0.2;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    box-shadow: var(--glow-red);
}

/* ===== CONTAINER ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

section {
    padding: 5rem 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-line {
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    margin: 1rem auto;
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== AI 3D BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    font-size: 0.95rem;
    font-family: inherit;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 
        0 8px 25px var(--shadow-red),
        0 0 20px rgba(220, 38, 38, 0.3);
    border: 1px solid var(--primary-color);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.btn-primary:hover {
    transform: translateY(-3px) rotateX(5deg);
    box-shadow: 
        0 12px 35px var(--shadow-red),
        0 0 30px rgba(220, 38, 38, 0.5);
}

.btn-secondary {
    background: rgba(220, 38, 38, 0.1);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 
        0 4px 15px rgba(220, 38, 38, 0.2),
        inset 0 0 20px rgba(220, 38, 38, 0.1);
    text-shadow: 0 0 10px var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px) rotateX(5deg);
    box-shadow: 
        0 8px 25px var(--shadow-red),
        0 0 20px rgba(220, 38, 38, 0.4);
}

.btn-outline {
    background: rgba(15, 15, 15, 0.8);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px) rotateX(5deg);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.2),
        0 0 15px rgba(220, 38, 38, 0.3);
}

/* ===== AI HERO SECTION WITH 3D EFFECTS ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding-top: 80px;
    background: var(--bg-primary);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 30%, rgba(220, 38, 38, 0.1) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(127, 29, 29, 0.1) 50%, transparent 70%);
    animation: heroPattern 10s ease-in-out infinite;
}

@keyframes heroPattern {
    0%, 100% { transform: translateX(-50px) translateY(-30px) rotate(0deg); }
    50% { transform: translateX(50px) translateY(30px) rotate(180deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 4rem;
    align-items: center;
    z-index: 2;
    position: relative;
}

.hero-greeting {
    margin-bottom: 1rem;
}

.greeting-text {
    font-size: 1.1rem;
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: var(--glow-red);
    animation: greetingGlow 3s ease-in-out infinite;
}

@keyframes greetingGlow {
    0%, 100% { text-shadow: 0 0 10px var(--primary-color); }
    50% { text-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1rem;
    color: var(--text-primary);
    perspective: 1000px;
}

.name-text {
    background: var(--gradient-ai);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    animation: gradientShift 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(220, 38, 38, 0.5);
    transform-style: preserve-3d;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-shadow: 0 0 15px rgba(252, 165, 165, 0.3);
    animation: subtitleFloat 6s ease-in-out infinite;
}

@keyframes subtitleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 500px;
    text-shadow: 0 0 10px rgba(252, 165, 165, 0.2);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    gap: 3rem;
}

.stat-item {
    text-align: center;
    transform-style: preserve-3d;
    animation: statFloat 4s ease-in-out infinite;
}

.stat-item:nth-child(2) { animation-delay: 0.5s; }
.stat-item:nth-child(3) { animation-delay: 1s; }

@keyframes statFloat {
    0%, 100% { transform: translateY(0px) rotateX(0deg); }
    50% { transform: translateY(-10px) rotateX(5deg); }
}

.stat-number {
    display: block;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    text-shadow: var(--glow-red);
    animation: numberPulse 2s ease-in-out infinite;
}

@keyframes numberPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.3);
}

.hero-avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    margin: 1rem 0;
}

.avatar-container {
    position: relative;
    width: 320px;
    height: 320px;
    margin: 0 auto;
    padding: 10px;
}

.avatar-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    box-shadow:
        0 0 20px var(--primary-color),
        inset 0 0 20px rgba(220, 38, 38, 0.2);
}

.avatar-image {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    border: 4px solid var(--primary-color);
    box-shadow:
        0 0 30px var(--shadow-red),
        0 0 60px var(--shadow-maroon),
        inset 0 0 20px rgba(220, 38, 38, 0.1);
    filter: drop-shadow(0 0 20px var(--primary-color));
    margin: 10px;
    display: block;
    position: relative;
    z-index: 2;
}

/* ===== AI ABOUT SECTION ===== */
.about {
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(127, 29, 29, 0.1) 0%, transparent 50%);
    animation: aboutPattern 15s ease-in-out infinite;
}

@keyframes aboutPattern {
    0%, 100% { transform: translateX(0px) translateY(0px); }
    50% { transform: translateX(30px) translateY(-20px); }
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.about-text h3 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-shadow: 0 0 15px rgba(220, 38, 38, 0.3);
}

.about-text p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
    text-shadow: 0 0 10px rgba(252, 165, 165, 0.2);
}

.highlight-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transition: all 0.3s ease;
}

.highlight-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);
    transition: left 0.5s ease;
}

.highlight-item:hover::before {
    left: 100%;
}

.highlight-item:hover {
    transform: translateY(-5px) rotateX(5deg);
    border-color: var(--primary-color);
    box-shadow:
        0 15px 35px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow:
        var(--glow-red),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 20px var(--primary-color); }
    50% { transform: scale(1.05); box-shadow: 0 0 30px var(--primary-color); }
}

.highlight-content h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.highlight-content p {
    color: var(--text-secondary);
    margin: 0;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.tech-item {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.tech-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(220, 38, 38, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tech-item:hover::before {
    opacity: 1;
}

.tech-item:hover {
    border-color: var(--primary-color);
    box-shadow:
        0 15px 35px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
    transform: translateY(-5px) rotateX(5deg);
}

.tech-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-shadow: var(--glow-red);
    animation: techIconFloat 4s ease-in-out infinite;
}

@keyframes techIconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.tech-item span {
    color: var(--text-primary);
    font-weight: 500;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.2);
}

/* ===== PROFILE SECTION ===== */
.profile {
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.profile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(127, 29, 29, 0.1) 0%, transparent 50%);
    animation: profilePattern 12s ease-in-out infinite;
}

@keyframes profilePattern {
    0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
    50% { transform: translateX(20px) translateY(-15px) rotate(2deg); }
}

.profile-content {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 4rem;
    align-items: start;
    position: relative;
    z-index: 2;
}

.profile-image-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 2rem;
    background: rgba(26, 10, 10, 0.3);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.profile-image-container {
    position: relative;
    width: 450px;
    height: 450px;
    margin: 0 auto;
    perspective: 1000px;
}

.profile-image-frame {
    position: relative;
    width: 400px;
    height: 400px;
    margin: 25px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 6px solid var(--primary-color);
    box-shadow:
        0 0 50px var(--shadow-red),
        0 0 100px var(--shadow-maroon),
        inset 0 0 40px rgba(220, 38, 38, 0.1);
    transform-style: preserve-3d;
    animation: profileImageFloat 6s ease-in-out infinite;
    background: linear-gradient(135deg, #0f0f0f, #1a0a0a, #2d1b1b);
    padding: 8px;
}

@keyframes profileImageFloat {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg);
        box-shadow: 0 0 50px var(--shadow-red), 0 0 100px var(--shadow-maroon), inset 0 0 40px rgba(220, 38, 38, 0.1);
    }
    50% {
        transform: translateY(-10px) rotateX(5deg);
        box-shadow: 0 0 60px var(--shadow-red), 0 0 120px var(--shadow-maroon), inset 0 0 50px rgba(220, 38, 38, 0.15);
    }
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    border-radius: 50%;
    filter: drop-shadow(0 0 25px var(--primary-color)) brightness(1.1) contrast(1.1);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #1a0a0a, #2d1b1b);
    padding: 10px;
}

.profile-image:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 35px var(--primary-color)) brightness(1.2) contrast(1.2);
}

/* Ensure full image visibility */
#profile-image {
    width: 95%;
    height: 95%;
    object-fit: contain;
    object-position: center;
    border-radius: 50%;
    margin: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(220, 38, 38, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.profile-image-frame:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-content i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.overlay-content p {
    font-weight: 600;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.profile-rings {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.ring {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.6;
}

.ring-1 {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    animation: ringRotate1 20s linear infinite;
    border-color: var(--primary-color);
}

.ring-2 {
    top: 15px;
    left: 15px;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    animation: ringRotate2 15s linear infinite reverse;
    border-color: var(--secondary-color);
}

.ring-3 {
    top: 30px;
    left: 30px;
    width: calc(100% - 60px);
    height: calc(100% - 60px);
    animation: ringRotate3 25s linear infinite;
    border-color: var(--accent-color);
}

@keyframes ringRotate1 {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes ringRotate2 {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes ringRotate3 {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.profile-quick-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quick-info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    transition: all 0.3s ease;
}

.quick-info-card:hover {
    transform: translateX(10px);
    border-color: var(--primary-color);
    box-shadow:
        0 12px 35px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: var(--glow-red);
}

.info-content h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.info-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.profile-details {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.profile-intro h3 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
    animation: nameGlow 4s ease-in-out infinite;
}

@keyframes nameGlow {
    0%, 100% { text-shadow: 0 0 20px rgba(220, 38, 38, 0.3); }
    50% { text-shadow: 0 0 30px rgba(220, 38, 38, 0.5), 0 0 40px rgba(220, 38, 38, 0.3); }
}

.profile-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.title-text {
    font-size: 1.3rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-shadow: 0 0 15px rgba(252, 165, 165, 0.3);
}

.title-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--gradient-primary);
    color: white;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: var(--glow-red);
    animation: badgePulse 3s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 20px var(--primary-color); }
    50% { transform: scale(1.05); box-shadow: 0 0 30px var(--primary-color); }
}

.profile-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    text-shadow: 0 0 10px rgba(252, 165, 165, 0.2);
}

.highlight-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.highlight-card {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.highlight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);
    transition: left 0.5s ease;
}

.highlight-card:hover::before {
    left: 100%;
}

.highlight-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow:
        0 15px 35px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: var(--glow-red);
    animation: highlightIconPulse 4s ease-in-out infinite;
}

@keyframes highlightIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.highlight-info h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.highlight-info p {
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.6;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

.stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 1rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    transition: all 0.3s ease;
    text-align: center;
}

.stat-card:hover {
    transform: translateY(-10px) rotateX(5deg);
    border-color: var(--primary-color);
    box-shadow:
        0 20px 40px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
    box-shadow: var(--glow-red);
    animation: statIconRotate 6s ease-in-out infinite;
}

@keyframes statIconRotate {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    text-shadow: var(--glow-red);
    animation: numberCount 2s ease-out;
}

@keyframes numberCount {
    from { transform: scale(0); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.profile-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

/* ===== RESUME MODAL ===== */
.resume-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: 12px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow:
        0 20px 60px var(--shadow-red),
        0 0 40px rgba(220, 38, 38, 0.3);
    border: 2px solid var(--border-color);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px) scale(0.9); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
}

.modal-header h3 {
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-shadow: 0 0 10px var(--primary-color);
}

.modal-header h3 i {
    color: var(--primary-color);
    text-shadow: var(--glow-red);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: var(--primary-color);
    background: rgba(220, 38, 38, 0.1);
    transform: rotate(90deg);
}

.modal-body {
    padding: 2rem;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

.resume-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.resume-viewer {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    box-shadow: inset 0 0 20px rgba(220, 38, 38, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-content,
    .about-content,
    .contact-content,
    .profile-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .certifications-grid,
    .skills-categories {
        grid-template-columns: 1fr;
    }

    .profile-image-container {
        width: 380px;
        height: 380px;
    }

    .profile-image-frame {
        width: 350px;
        height: 350px;
    }

    .highlight-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .avatar-container {
        width: 280px;
        height: 280px;
    }

    .avatar-image {
        width: 260px;
        height: 260px;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--bg-primary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 2rem 0;
        transition: left 0.3s ease;
        border-top: 2px solid var(--border-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .avatar-container {
        width: 240px;
        height: 240px;
    }

    .avatar-image {
        width: 220px;
        height: 220px;
    }

    .hero-avatar {
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .profile-image-container {
        width: 320px;
        height: 320px;
    }

    .profile-image-frame {
        width: 280px;
        height: 280px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .title-text {
        font-size: 1.1rem;
    }

    .profile-intro h3 {
        font-size: 2rem;
    }
}

/* ===== SKILLS SECTION ===== */
.skills {
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.skills::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(127, 29, 29, 0.1) 0%, transparent 50%);
    animation: skillsPattern 18s ease-in-out infinite;
}

@keyframes skillsPattern {
    0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
    50% { transform: translateX(-20px) translateY(15px) rotate(-1deg); }
}

.skills-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.skill-category {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    transition: all 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow:
        0 15px 35px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
}

.skill-category h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: block;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 500;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 2s ease;
    box-shadow: 0 0 10px var(--primary-color);
    animation: skillGlow 3s ease-in-out infinite;
}

@keyframes skillGlow {
    0%, 100% { box-shadow: 0 0 10px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

/* ===== CERTIFICATIONS SECTION ===== */
.certifications {
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.certifications::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 70% 30%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 30% 70%, rgba(127, 29, 29, 0.1) 0%, transparent 50%);
    animation: certificationsPattern 16s ease-in-out infinite;
}

@keyframes certificationsPattern {
    0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
    50% { transform: translateX(25px) translateY(-20px) rotate(1deg); }
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.certification-card {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.certification-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);
    transition: left 0.5s ease;
}

.certification-card:hover::before {
    left: 100%;
}

.certification-card:hover {
    transform: translateY(-10px) rotateX(5deg);
    border-color: var(--primary-color);
    box-shadow:
        0 20px 40px rgba(220, 38, 38, 0.2),
        0 0 30px rgba(220, 38, 38, 0.1);
}

.cert-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: var(--glow-red);
    animation: certIconPulse 4s ease-in-out infinite;
}

@keyframes certIconPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 20px var(--primary-color); }
    50% { transform: scale(1.1); box-shadow: 0 0 30px var(--primary-color); }
}

.cert-info h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.cert-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.cert-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--gradient-primary);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: var(--glow-red);
    margin-bottom: 1rem;
}

.cert-view-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(220, 38, 38, 0.1);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 4px 15px rgba(220, 38, 38, 0.2),
        inset 0 0 20px rgba(220, 38, 38, 0.1);
}

.cert-view-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px var(--shadow-red),
        0 0 20px rgba(220, 38, 38, 0.4);
}

.certification-card {
    cursor: pointer;
}

.certification-card:hover .cert-view-btn {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* ===== CERTIFICATE MODAL ===== */
.certificate-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease;
}

.certificate-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.certificate-viewer {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    box-shadow: inset 0 0 20px rgba(220, 38, 38, 0.1);
    text-align: center;
}

.certificate-viewer img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow:
        0 4px 20px rgba(220, 38, 38, 0.3),
        0 0 40px rgba(220, 38, 38, 0.1);
    transition: all 0.3s ease;
}

.certificate-viewer img:hover {
    transform: scale(1.02);
    box-shadow:
        0 8px 30px rgba(220, 38, 38, 0.4),
        0 0 50px rgba(220, 38, 38, 0.2);
}

/* ===== CONTACT SECTION ===== */
.contact {
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 40% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 60% 80%, rgba(127, 29, 29, 0.1) 0%, transparent 50%);
    animation: contactPattern 14s ease-in-out infinite;
}

@keyframes contactPattern {
    0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
    50% { transform: translateX(-15px) translateY(10px) rotate(-0.5deg); }
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    position: relative;
    z-index: 2;
}

.contact-info h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
    text-shadow: 0 0 15px rgba(220, 38, 38, 0.3);
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    text-shadow: 0 0 10px rgba(252, 165, 165, 0.2);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 4px 15px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.05);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(10px);
    border-color: var(--primary-color);
    box-shadow:
        0 8px 25px rgba(220, 38, 38, 0.2),
        0 0 20px rgba(220, 38, 38, 0.1);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    box-shadow: var(--glow-red);
}

.contact-details h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.contact-form-section h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    text-shadow: 0 0 15px rgba(220, 38, 38, 0.3);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: inset 0 0 10px rgba(220, 38, 38, 0.05);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow:
        0 0 0 3px rgba(220, 38, 38, 0.1),
        inset 0 0 20px rgba(220, 38, 38, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-primary);
    border-top: 2px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 50% 0%, rgba(220, 38, 38, 0.1) 0%, transparent 50%);
    animation: footerPattern 20s ease-in-out infinite;
}

@keyframes footerPattern {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
    padding: 3rem 0;
    position: relative;
    z-index: 2;
}

.footer-info h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 15px rgba(220, 38, 38, 0.3);
}

.footer-info p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(252, 165, 165, 0.2);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--glow-red);
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px var(--shadow-red),
        0 0 30px rgba(220, 38, 38, 0.5);
}

.footer-links h4,
.footer-contact h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.footer-links a:hover {
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.footer-contact p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}

.footer-contact i {
    color: var(--primary-color);
    text-shadow: var(--glow-red);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    text-align: center;
    position: relative;
    z-index: 2;
}

.footer-bottom p {
    color: var(--text-secondary);
    margin: 0;
    text-shadow: 0 0 5px rgba(252, 165, 165, 0.2);
}
