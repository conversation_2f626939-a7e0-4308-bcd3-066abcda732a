// ===== AI PORTFOLIO APPLICATION ===== //

// Initialize Portfolio
document.addEventListener('DOMContentLoaded', function() {
    initializePortfolio();
});

// Initialize Portfolio Components
function initializePortfolio() {
    setupNavigation();
    setupContactForm();
    createAIParticles();
    console.log('✅ AI Portfolio initialized successfully!');
}

// Create AI Particles
function createAIParticles() {
    const particlesContainer = document.getElementById('ai-particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Random positioning and animation delay
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        
        // Random particle size
        const size = Math.random() * 3 + 1;
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        
        // Random opacity
        particle.style.opacity = Math.random() * 0.8 + 0.2;
        
        particlesContainer.appendChild(particle);
    }
}

// Resume Modal Functions
function showResumeModal() {
    const modal = document.getElementById('resume-modal');
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeResumeModal() {
    const modal = document.getElementById('resume-modal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('resume-modal');
    if (e.target === modal) {
        closeResumeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeResumeModal();
        closeCertificateModal();
    }
});

// Certificate Modal Functions
let currentCertificate = '';

function showCertificate(certType) {
    const modal = document.getElementById('certificate-modal');
    const certImage = document.getElementById('cert-image');
    const certTitle = document.getElementById('cert-title-text');
    const downloadBtn = document.getElementById('download-cert-btn');

    currentCertificate = certType;

    // Certificate data with image paths
    const certificates = {
        'java': {
            title: 'Java Programming Certificate',
            image: 'course certificate/Sanjai S Java Certificate.pdf',
            filename: 'Java_Programming_Certificate.pdf'
        },
        'python': {
            title: 'Python Development Certificate',
            image: 'course certificate/Sanjai S Python Certificate.pdf',
            filename: 'Python_Development_Certificate.pdf'
        },
        'cpp': {
            title: 'C++ Programming Certificate',
            image: 'course certificate/Sanjai S C++ Certificate.pdf',
            filename: 'CPP_Programming_Certificate.pdf'
        }
    };

    const cert = certificates[certType];
    if (cert) {
        certTitle.textContent = cert.title;
        certImage.src = cert.image;
        certImage.alt = cert.title;
        downloadBtn.setAttribute('data-filename', cert.filename);

        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

function closeCertificateModal() {
    const modal = document.getElementById('certificate-modal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
    currentCertificate = '';
}

function downloadCertificate() {
    const certificates = {
        'java': {
            image: 'course certificate/Sanjai S Java Certificate.pdf',
            filename: 'Java_Programming_Certificate.pdf'
        },
        'python': {
            image: 'course certificate/Sanjai S Python Certificate.pdf',
            filename: 'Python_Development_Certificate.pdf'
        },
        'cpp': {
            image: 'course certificate/Sanjai S C++ Certificate.pdf',
            filename: 'CPP_Programming_Certificate.pdf'
        }
    };

    const cert = certificates[currentCertificate];
    if (cert) {
        const link = document.createElement('a');
        link.href = cert.image;
        link.download = cert.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification(`${cert.filename} downloaded successfully!`, 'success');
    }
}

function printCertificate() {
    const certImage = document.getElementById('cert-image');
    if (certImage.src) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Print Certificate</title>
                    <style>
                        body { margin: 0; padding: 20px; text-align: center; }
                        img { max-width: 100%; height: auto; }
                        @media print { body { margin: 0; padding: 0; } }
                    </style>
                </head>
                <body>
                    <img src="${certImage.src}" alt="Certificate" />
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    }
}

// Close certificate modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('certificate-modal');
    if (e.target === modal) {
        closeCertificateModal();
    }
});

// Navigation Setup
function setupNavigation() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Mobile navigation toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Active navigation highlighting
    window.addEventListener('scroll', updateActiveNavigation);
}

// Update Active Navigation
function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// Print Resume Function
function printResume() {
    const resumeUrl = 'resume/SANJAI PROFILE RESUME.pdf';
    const printWindow = window.open(resumeUrl, '_blank');
    if (printWindow) {
        printWindow.onload = function() {
            printWindow.print();
        };
    } else {
        showNotification('Please allow popups to print the resume', 'info');
    }
}

// Contact Form Setup
function setupContactForm() {
    const form = document.querySelector('.contact-form');
    if (form) {
        // Add input focus effects
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    }
}

// Contact Form Submission
function handleContactSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Sending...</span>';
    submitBtn.disabled = true;
    
    // Simulate form submission (replace with actual form handling)
    setTimeout(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        showNotification('Message sent successfully! I\'ll get back to you soon.', 'success');
        
        // Reset form
        form.reset();
        
        // Log form data (for development)
        console.log('Contact form submitted:', data);
    }, 2000);
}

// Certification Viewer
function viewCertification(title, message) {
    showNotification(`${title}: ${message}`, 'info');
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#059669' : type === 'error' ? '#dc2626' : '#2563eb'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        max-width: 400px;
        border: 1px solid ${type === 'success' ? '#047857' : type === 'error' ? '#b91c1c' : '#1d4ed8'};
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Add notification styles
const style = document.createElement('style');
style.textContent = `
    .notification-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    .focused {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
    }
`;
document.head.appendChild(style);

// Performance monitoring
window.addEventListener('load', function() {
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    console.log(`⚡ Portfolio loaded in ${loadTime}ms`);
    
    if (loadTime < 2000) {
        console.log('🚀 Excellent load time!');
    } else if (loadTime < 5000) {
        console.log('✅ Good load time');
    } else {
        console.warn('⚠️ Consider optimizing for better performance');
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('Portfolio Error:', e.error);
});

// Success message
console.log('🎉 Sanjai S - AI Developer Portfolio');
console.log('🤖 Professional AI & Machine Learning Engineer');
console.log('📧 Contact: <EMAIL>');
console.log('🔗 LinkedIn: https://linkedin.com/in/sanjai-s');
console.log('✨ Portfolio loaded successfully!');
